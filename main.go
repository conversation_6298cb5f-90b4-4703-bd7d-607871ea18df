package main

import (
	"bufio"
	"bytes"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"gopkg.in/ini.v1"
)

type Config struct {
	Addresses       []string
	Threshold       int
	LogFile         string
	Interval        int
	NotifyUrl       string
	ServerName      string
	Ports           []string
	ClientThreshold int
	RestartTimes    []string
}

func loadConfig() (*Config, error) {
	cfg, err := ini.Load("C:\\Users\\<USER>\\Desktop\\HgMonitor\\config.ini")
	if err != nil {
		return nil, err
	}

	addresses := strings.Split(cfg.Section("monitor").Key("addresses").String(), ",")
	threshold, err := cfg.Section("monitor").Key("threshold").Int()
	if err != nil {
		return nil, err
	}
	logFile := cfg.Section("monitor").Key("logfile").String()

	interval, err := cfg.Section("monitor").Key("interval").Int()
	if err != nil {
		interval = 5
	}

	notifyUrl := cfg.Section("monitor").Key("notify_url").String()
	serverName := cfg.Section("monitor").Key("server_name").String()
	ports := strings.Split(cfg.Section("monitor").Key("ports").String(), ",")

	clientThreshold, err := cfg.Section("monitor").Key("client_threshold").Int()
	if err != nil {
		clientThreshold = 30
	}

	restartTimes := strings.Split(cfg.Section("monitor").Key("restart_times").String(), ",")

	return &Config{
		Addresses:       addresses,
		Threshold:       threshold,
		LogFile:         logFile,
		Interval:        interval,
		NotifyUrl:       notifyUrl,
		ServerName:      serverName,
		Ports:           ports,
		ClientThreshold: clientThreshold,
		RestartTimes:    restartTimes,
	}, nil
}

func setupLogging(logFile string) error {
	logDir := filepath.Dir(logFile)
	if _, err := os.Stat(logDir); os.IsNotExist(err) {
		err = os.MkdirAll(logDir, 0755)
		if err != nil {
			return fmt.Errorf("创建日志文件目录失败: %v", err)
		}
	}

	file, err := os.OpenFile(logFile, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0666)
	if err != nil {
		return fmt.Errorf("打开日志文件失败: %v", err)
	}
	log.SetOutput(file)
	return nil
}

func executeNetstat() (string, error) {
	cmd := exec.Command("netstat", "-ano")
	var out bytes.Buffer
	cmd.Stdout = &out
	err := cmd.Run()
	if err != nil {
		return "", err
	}
	return out.String(), nil
}

func parseNetstatOutput(output string, addresses []string) map[string]int {
	scanner := bufio.NewScanner(strings.NewReader(output))
	pidCount := make(map[string]int)

	for scanner.Scan() {
		line := scanner.Text()
		if strings.Contains(line, "ESTABLISHED") {
			for _, addr := range addresses {
				if strings.Contains(line, addr) {
					fields := strings.Fields(line)
					if len(fields) > 4 {
						pid := fields[len(fields)-1]
						pidCount[pid]++
					}
				}
			}
		}
	}

	return pidCount
}

func parseNetstatOutputByPort(output string, ports []string) map[string]int {
	scanner := bufio.NewScanner(strings.NewReader(output))
	pidCount := make(map[string]int)

	for scanner.Scan() {
		line := scanner.Text()
		if strings.Contains(line, "ESTABLISHED") {
			for _, port := range ports {
				fields := strings.Fields(line)
				if strings.Split(fields[1], ":")[1] == port {
					pid := fields[len(fields)-1]
					pidCount[pid]++
				}
			}
		}
	}

	return pidCount
}

// func getListeningPort(pid string) (string, error) {
// 	cmd := exec.Command("netstat", "-aon")
// 	var out bytes.Buffer
// 	cmd.Stdout = &out
// 	err := cmd.Run()
// 	if err != nil {
// 		return "", err
// 	}

// 	scanner := bufio.NewScanner(strings.NewReader(out.String()))

// 	for scanner.Scan() {
// 		line := scanner.Text()
// 		fields := strings.Fields(line)

// 		if len(fields) > 4 && fields[len(fields)-1] == pid && strings.Contains(line, "LISTENING") {
// 			port := strings.Split(fields[1], ":")[1]
// 			return port, nil
// 		}
// 	}

// 	return "", fmt.Errorf("未找到PID %s 监听的端口", pid)
// }

func getListeningPortMap(t string) (map[string]string, error) {
	portMap := make(map[string]string)
	output, err := executeNetstat()
	if err != nil {
		return portMap, err
	}

	scanner := bufio.NewScanner(strings.NewReader(output))

	for scanner.Scan() {
		line := scanner.Text()
		fields := strings.Fields(line)

		if len(fields) > 4 && strings.Contains(line, "LISTENING") {
			port := strings.Split(fields[1], ":")[1]
			pid := fields[len(fields)-1]
			if t == "pid" {
				portMap[pid] = port
			} else {
				portMap[port] = pid
			}
		}
	}

	return portMap, nil
}

func restartServer(port string, pid string) error {
	serverPath := fmt.Sprintf("C:\\Users\\<USER>\\Desktop\\HgGroup\\server_%s\\HgGroupServer.exe", port)
	cmd := exec.Command("taskkill", "/F", "/PID", pid)
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("kill进程失败: %v", err)
	}
	time.Sleep(2 * time.Second)
	cmd = exec.Command(serverPath)
	return cmd.Start()
}

func startServer(port string) error {
	serverPath := fmt.Sprintf("C:\\Users\\<USER>\\Desktop\\HgGroup\\server_%s\\HgGroupServer.exe", port)
	cmd := exec.Command(serverPath)
	return cmd.Start()
}

func restartServerByPort(port string) error {
	// 通过port找到pid
	portMap, err := getListeningPortMap("port")
	if err != nil {
		return fmt.Errorf("获取端口映射失败: %v", err)
	}

	pid, ok := portMap[port]
	if !ok {
		log.Printf("未找到端口 %s 对应的PID，准备启动", port)
		return startServer(port)
	}

	return restartServer(port, pid)
}

// func restartAllHgGroupServers(ports []string) error {
// 	// 杀掉所有名为 HgGroupServer 的进程
// 	cmd := exec.Command("taskkill", "/F", "/IM", "HgGroupServer.exe")
// 	if err := cmd.Run(); err != nil {
// 		return fmt.Errorf("重启HgGroupServer进程失败: %v", err)
// 	}

// 	time.Sleep(5 * time.Second)

// 	var serverPaths []string

// 	for _, port := range ports {
// 		serverPaths = append(serverPaths, fmt.Sprintf("C:\\Users\\<USER>\\Desktop\\HgGroup\\server_%s\\HgGroupServer.exe", port))
// 	}

// 	for _, path := range serverPaths {
// 		cmd := exec.Command(path)
// 		if err := cmd.Start(); err != nil {
// 			return fmt.Errorf("启动服务失败: %v", err)
// 		}
// 	}

// 	log.Println("HgGroupServer 进程已重启")
// 	return nil
// }

func scheduleDailyRestart(times []string, ports []string) {
	for _, timeStr := range times {
		go func(t string) {
			parts := strings.Split(t, ":")
			if len(parts) != 2 {
				log.Printf("无效的重启时间格式: %s", t)
				return
			}

			hour, err1 := strconv.Atoi(parts[0])
			minute, err2 := strconv.Atoi(parts[1])
			if err1 != nil || err2 != nil {
				log.Printf("无效的时间值: %s", t)
				return
			}

			now := time.Now()
			nextRestart := time.Date(now.Year(), now.Month(), now.Day(), hour, minute, 0, 0, now.Location())

			// 如果当前时间已经过了计划重启时间，设置为第二天的重启时间
			if now.After(nextRestart) {
				nextRestart = nextRestart.Add(24 * time.Hour)
			}

			durationUntilRestart := time.Until(nextRestart)
			log.Printf("计划在 %s 进行重启", nextRestart.Format("2006-01-02 15:04:05"))

			time.AfterFunc(durationUntilRestart, func() {
				for _, port := range ports {
					err := restartServerByPort(port)
					if err != nil {
						log.Printf("在 %s 的重启失败: %v", port, err)
					}
				}

				// 重新设置定时器，确保每天都执行重启
				scheduleDailyRestart([]string{t}, ports)
			})
		}(timeStr)
	}
}

func displayConfig(config *Config) {
	// 固定的第一列宽度
	const firstColWidth = 15

	// 获取终端宽度（假设80列，如果需要动态获取，可以使用其他库）
	terminalWidth := 110
	if width := os.Getenv("COLUMNS"); width != "" {
		if w, err := strconv.Atoi(width); err == nil {
			terminalWidth = w
		}
	}

	// 计算第二列宽度
	secondColWidth := terminalWidth - firstColWidth - 5 // 减去分隔符和空格的宽度

	// 打印标题
	fmt.Println(strings.Repeat("=", terminalWidth+2))
	fmt.Printf("\n" + fmt.Sprintf("%*s", -terminalWidth/2, "HGMONITOR SERVICE") + "\n\n")
	fmt.Println(strings.Repeat("=", terminalWidth+2))
	fmt.Println()

	// 打印标题
	fmt.Printf("| %-*s | %-*s |\n", firstColWidth, "Key", secondColWidth, "Value")
	fmt.Println("|" + strings.Repeat("-", firstColWidth+2) + "|" + strings.Repeat("-", secondColWidth+2) + "|")

	// 打印配置项
	fmt.Printf("| %-*s | %-*s |\n", firstColWidth, "Addresses", secondColWidth, strings.Join(config.Addresses, ", "))
	fmt.Printf("| %-*s | %-*s |\n", firstColWidth, "Ports", secondColWidth, strings.Join(config.Ports, ", "))
	fmt.Printf("| %-*s | %-*d |\n", firstColWidth, "Threshold", secondColWidth, config.Threshold)
	fmt.Printf("| %-*s | %-*d |\n", firstColWidth, "ClientThreshold", secondColWidth, config.ClientThreshold)
	fmt.Printf("| %-*s | %-*s |\n", firstColWidth, "LogFile", secondColWidth, config.LogFile)
	fmt.Printf("| %-*s | %-*d |\n", firstColWidth, "Interval", secondColWidth, config.Interval)
	fmt.Printf("| %-*s | %-*s |\n", firstColWidth, "NotifyUrl", secondColWidth, config.NotifyUrl)
	fmt.Printf("| %-*s | %-*s |\n", firstColWidth, "ServerName", secondColWidth, config.ServerName)
	fmt.Printf("| %-*s | %-*s |\n", firstColWidth, "RestartTimes", secondColWidth, strings.Join(config.RestartTimes, ", "))

	// 打印结束行
	fmt.Println()
}

func main() {
	// 记录程序启动时间
	startTime := time.Now()

	config, err := loadConfig()
	if err != nil {
		log.Fatalf("配置文件加载失败: %v", err)
	}

	// 定时任务
	scheduleDailyRestart(config.RestartTimes, config.Ports)

	displayConfig(config)

	if err := setupLogging(config.LogFile); err != nil {
		log.Fatalf("日志文件打开失败: %v", err)
	}

	ticker := time.NewTicker(time.Duration(config.Interval) * time.Second)
	defer ticker.Stop()

	fmt.Println("守护程序启动成功，请勿关闭窗口...")
	for {
		select {
		case <-ticker.C:
			output, err := executeNetstat()
			if err != nil {
				log.Printf("netstat执行失败: %v", err)
				continue
			}

			// 获取监听端口
			portMap, err := getListeningPortMap("pid")
			if err != nil {
				log.Fatalf("获取监听端口失败: %v", err)
			}

			// 数据库连接数
			pidCount := parseNetstatOutput(output, config.Addresses)
			for pid, count := range pidCount {
				if count > config.Threshold {
					log.Printf("PID %s 连接数 %d 大于阈值 %d", pid, count, config.Threshold)
					port := portMap[pid]

					fmt.Printf("\n%s 数据库连接数 %d 大于阈值 %d，准备重启...\n", port, count, config.Threshold)

					log.Printf("重启端口为 %s 的服务端", port)
					// 重启对应的服务器
					if err := restartServer(port, pid); err != nil {
						log.Printf("重启端口为 %s 的服务端失败: %v", port, err)
					}

					if err := sendNotify(config.NotifyUrl, port, config.ServerName, count, "数据库连接数"); err != nil {
						log.Printf("发送通知失败: %v", err)
					}
					fmt.Printf("%s 重启成功\n", port)
				}
			}

			// 客户端连接数
			if time.Since(startTime) > time.Minute {
				clientPidCount := parseNetstatOutputByPort(output, config.Ports)
				for pid, count := range clientPidCount {
					if count > config.ClientThreshold {
						port := portMap[pid]

						fmt.Printf("\n%s 服务端连接数 %d 大于阈值 %d，准备重启...\n", port, count, config.ClientThreshold)

						log.Printf("重启端口为 %s 的服务端", port)
						// 重启对应的服务器
						if err := restartServer(port, pid); err != nil {
							log.Printf("重启端口为 %s 的服务端失败: %v", port, err)
						}

						if err := sendNotify(config.NotifyUrl, port, config.ServerName, count, "服务端连接数"); err != nil {
							log.Printf("发送通知失败: %v", err)
						}
						fmt.Printf("%s 重启成功\n", port)
						startTime = time.Now()
					}
				}
			}
		}
	}
}

func sendNotify(url, port, name string, count int, tp string) error {
	contentText := fmt.Sprintf("检测到服务器 %s:%s 出现异常状况，%s[%d]。为确保系统稳定性和服务的连续性，系统已自动执行重启操作。", name, port, tp, count)
	data := map[string]interface{}{
		"msg_type": "text",
		"content": map[string]string{
			"text": contentText,
		},
	}
	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}
	_, err = http.Post(url, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return err
	}
	return nil
}
