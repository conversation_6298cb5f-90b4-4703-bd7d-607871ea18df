package main

import (
	"bufio"
	"bytes"
	"fmt"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"

	"github.com/kardianos/service"
	"gopkg.in/ini.v1"
)

type program struct{}

type Config struct {
	Addresses []string
	Threshold int
	LogFile   string
	Interval  int
}

func (p *program) Start(s service.Service) error {
	// 启动服务
	go p.run()
	return nil
}

func (p *program) run() {
	// 这里是你的主要监控逻辑
	config, err := loadConfig()
	if err != nil {
		log.Fatalf("配置文件加载失败: %v", err)
	}

	if err := setupLogging(config.LogFile); err != nil {
		log.Fatalf("日志文件打开失败: %v", err)
	}

	ticker := time.NewTicker(time.Duration(config.Interval) * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			output, err := executeNetstat()
			if err != nil {
				log.Printf("netstat执行失败: %v", err)
				continue
			}

			pidCount := parseNetstatOutput(output, config.Addresses)
			for pid, count := range pidCount {
				if count > config.Threshold {
					log.Printf("PID %s 连接数据大于阈值 %d", pid, count)

					// 获取监听端口
					port, err := getListeningPort(pid)
					if err != nil {
						log.Printf("%v", err)
						continue
					}

					log.Printf("重启端口为 %s 的服务端", port)
					// 重启对应的服务器
					if err := restartServer(port, pid); err != nil {
						log.Printf("重启端口为 %s 的服务端失败: %v", port, err)
					}
				}
			}
		}
	}
}

func (p *program) Stop(s service.Service) error {
	// 停止服务的逻辑
	return nil
}

func loadConfig() (*Config, error) {
	cfg, err := ini.Load("C:\\Users\\<USER>\\Desktop\\HgMonitor\\config.ini")
	if err != nil {
		return nil, err
	}

	addresses := strings.Split(cfg.Section("monitor").Key("addresses").String(), ",")
	threshold, err := cfg.Section("monitor").Key("threshold").Int()
	if err != nil {
		return nil, err
	}
	logFile := cfg.Section("monitor").Key("logfile").String()

	interval, err := cfg.Section("monitor").Key("interval").Int()
	if err != nil {
		interval = 5
	}

	return &Config{
		Addresses: addresses,
		Threshold: threshold,
		LogFile:   logFile,
		Interval:  interval,
	}, nil
}

func setupLogging(logFile string) error {
	logDir := filepath.Dir(logFile)
	if _, err := os.Stat(logDir); os.IsNotExist(err) {
		err = os.MkdirAll(logDir, 0755)
		if err != nil {
			return fmt.Errorf("创建日志文件目录失败: %v", err)
		}
	}

	file, err := os.OpenFile(logFile, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0666)
	if err != nil {
		return fmt.Errorf("打开日志文件失败: %v", err)
	}
	log.SetOutput(file)
	return nil
}

func executeNetstat() (string, error) {
	cmd := exec.Command("netstat", "-ano")
	var out bytes.Buffer
	cmd.Stdout = &out
	err := cmd.Run()
	if err != nil {
		return "", err
	}
	return out.String(), nil
}

func parseNetstatOutput(output string, addresses []string) map[string]int {
	scanner := bufio.NewScanner(strings.NewReader(output))
	pidCount := make(map[string]int)

	for scanner.Scan() {
		line := scanner.Text()
		if strings.Contains(line, "ESTABLISHED") {
			for _, addr := range addresses {
				if strings.Contains(line, addr) {
					fields := strings.Fields(line)
					if len(fields) > 4 {
						pid := fields[len(fields)-1]
						pidCount[pid]++
					}
				}
			}
		}
	}

	return pidCount
}

func getListeningPort(pid string) (string, error) {
	cmd := exec.Command("netstat", "-aon")
	var out bytes.Buffer
	cmd.Stdout = &out
	err := cmd.Run()
	if err != nil {
		return "", err
	}

	scanner := bufio.NewScanner(strings.NewReader(out.String()))

	for scanner.Scan() {
		line := scanner.Text()
		fields := strings.Fields(line)

		if len(fields) > 4 && fields[len(fields)-1] == pid && strings.Contains(line, "LISTENING") {
			port := strings.Split(fields[1], ":")[1]
			return port, nil
		}
	}

	return "", fmt.Errorf("未找到PID %s 监听的端口", pid)
}

func restartServer(port string, pid string) error {
	serverPath := fmt.Sprintf("C:\\Users\\<USER>\\Desktop\\HgGroup\\server_%s\\HgGroupServer.exe", port)
	cmd := exec.Command("taskkill", "/F", "/PID", pid)
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("kill进程失败: %v", err)
	}
	cmd = exec.Command(serverPath, "/show")
	// cmd = exec.Command("cmd", "/c", "start", serverPath)
	return cmd.Start()
}

func main() {
	svcConfig := &service.Config{
		Name:        "HgMonitorService",
		DisplayName: "Hg Monitor Service",
		Description: "监控特定地址连接并在达到阈值时重启服务。",
	}

	prg := &program{}
	s, err := service.New(prg, svcConfig)
	if err != nil {
		log.Fatal(err)
	}

	logger, err := s.Logger(nil)
	if err != nil {
		log.Fatal(err)
	}

	if len(os.Args) > 1 {
		err = service.Control(s, os.Args[1])
		if err != nil {
			log.Fatal(err)
		}
		return
	}

	err = s.Run()
	if err != nil {
		logger.Error(err)
	}
}
